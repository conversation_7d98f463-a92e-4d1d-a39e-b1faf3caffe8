﻿using Bogus;
using global::HolyBless.Entities.Articles;
using HolyBless.Enums;

namespace HolyBless.FakeData.DataSeeders
{
    namespace HolyBless.FakeData.DataSeeders
    {
        public static class ArticleDataHansGenerator
        {
            private static readonly string[] ChineseArticleTitles =
            [
                "心灵觉醒的旅程", "智慧之光照亮人生", "修行路上的感悟", "爱与慈悲的力量",
            "生命的真谛与意义", "梦境中的启示", "天门开启的奇迹", "平安祝福的力量",
            "心灵成长的秘诀", "修行答疑解惑", "生死哲学思考", "微博生活感悟",
            "回头是岸的故事", "师恩如山的教诲", "修行心得分享", "温故知新的智慧",
            "虔诚忏悔的力量", "神迹见证录", "人生感悟随笔", "生活智慧点滴",
            "心灵净化之路", "修行体验分享", "生命觉醒时刻", "爱的温暖传递",
            "智慧人生指南", "心灵平静之道", "修行困惑解答", "生活哲学思考",
            "梦想成真的力量", "神恩浩荡见证", "师父慈悲教导", "忏悔重生之路",
            "奇迹人生故事", "温暖人心的爱", "心灵升华体验", "修行智慧分享",
            "生命意义探索", "梦境奇遇记", "天启之路指引", "平安护佑见证",
            "心灵觉醒感悟", "修行方法指导", "人生智慧总结", "生活感言录",
            "浪子回头金不换", "恩师智慧传承", "修行收获心得", "历史启示录",
            "忏悔心声倾诉", "神迹恩典见证", "人生思考随笔", "生活点滴记录"
            ];

            private static readonly string[] ChineseDescriptions =
            [
                "分享心灵成长的体验，探索内心深处的智慧与力量。",
            "记录修行路上的感悟与体验，为修行者提供指引。",
            "用爱与慈悲温暖人心，传递正能量与希望。",
            "探索生命的真谛，分享人生的智慧与感悟。",
            "记录梦境中的奇遇与启示，解析梦的深层含义。",
            "见证天门开启的奇迹，感受神圣的力量与护佑。",
            "分享平安祝福的力量，传递神恩浩荡的见证。",
            "指导心灵成长的方法，帮助心灵净化与升华。",
            "解答修行路上的疑惑，为修行者指明方向。",
            "思考生死的奥秘，探索人生的哲学与智慧。",
            "记录生活的点滴感悟，分享微博中的智慧。",
            "分享回头是岸的故事，见证重新开始的力量。",
            "传承师恩如山的教诲，铭记恩师的慈悲与智慧。",
            "分享修行路上的心得体会，记录修行的点点滴滴。",
            "温故而知新，从历史中汲取智慧与启示。",
            "虔诚忏悔洗涤心灵，分享忏悔的感悟与体验。",
            "记录神迹显现的见证，分享奇迹的故事与感悟。",
            "随笔记录人生感悟，分享生活的智慧与思考。",
            "点滴记录生活智慧，传递温暖与正能量。",
            "指引心灵净化之路，帮助心灵觉醒与成长。"
            ];

            private static readonly string[] ChineseKeywords =
            [
                "心灵,觉醒,智慧", "修行,感悟,体验", "爱,慈悲,温暖", "生命,真谛,意义",
            "梦境,启示,奇遇", "天门,奇迹,神圣", "平安,祝福,护佑", "心灵,成长,净化",
            "修行,答疑,指导", "生死,哲学,智慧", "微博,生活,感悟", "回头,悔过,重生",
            "师恩,教诲,慈悲", "修行,心得,分享", "温故,知新,历史", "忏悔,洗涤,重生",
            "神迹,见证,奇迹", "人生,感悟,智慧", "生活,点滴,温暖", "心灵,平静,升华"
            ];

            private static readonly string[] ChineseContentTemplates =
            [
                "人生如河，岁月如歌。在这个充满变化的世界里，我们每个人都在寻找属于自己的道路。修行不是逃避现实，而是更好地面对生活中的挑战。当我们用心去感受生活的每一个瞬间，用爱去拥抱每一个遇见，我们就能在平凡中发现不平凡的美。\n\n心灵的成长需要时间，就像花朵需要阳光和雨露一样。在修行的路上，我们会遇到各种困难和挑战，但正是这些经历让我们变得更加坚强和智慧。每一次的跌倒都是为了更好的站起来，每一次的迷失都是为了找到真正的方向。\n\n让我们用感恩的心去看待生活中的每一个经历，用慈悲的心去对待身边的每一个人。只有这样，我们才能真正体会到生命的美好和意义。",

            "在这个快节奏的时代，我们常常忘记停下来聆听内心的声音。其实，智慧就在我们的内心深处，等待着我们去发现。修行的目的不是为了获得什么外在的东西，而是为了回归内心的宁静与和谐。\n\n每个人都有自己的修行方式，有些人通过冥想，有些人通过阅读，有些人通过服务他人。无论选择哪种方式，最重要的是要有一颗虔诚的心和持续的努力。修行不是一朝一夕的事情，而是一个终身的过程。\n\n当我们真正开始修行时，会发现生活中的很多问题都变得简单了。因为我们学会了用智慧去看待问题，用慈悲去化解矛盾，用包容去接纳差异。这就是修行给我们带来的最大礼物。",

            "爱是这个世界上最强大的力量，它能够化解一切的仇恨和偏见。当我们用爱去看待这个世界时，我们会发现每个人都有自己的故事，每个人都值得被理解和关爱。\n\n慈悲不仅仅是对他人的同情，更是对自己的宽容。我们要学会原谅自己的过错，接纳自己的不完美。只有当我们真正爱自己时，我们才能真正爱别人。\n\n在日常生活中，我们可以通过小小的行为来传递爱和温暖。一个微笑、一句问候、一个拥抱，这些看似微不足道的行为，却能够给他人带来巨大的力量。让我们成为爱的传递者，用我们的行动去温暖这个世界。",

            "生命是一段奇妙的旅程，每个人都有自己独特的使命和价值。我们来到这个世界上，不是为了索取，而是为了给予；不是为了抱怨，而是为了感恩；不是为了破坏，而是为了建设。\n\n在寻找生命意义的过程中，我们会遇到各种各样的困惑和挑战。但正是这些经历让我们变得更加成熟和智慧。每一次的挫折都是成长的机会，每一次的失败都是成功的铺垫。\n\n真正的成功不是财富的积累，不是名声的获得，而是内心的平静和满足。当我们能够坦然面对生活的起起落落，当我们能够用感恩的心去看待每一个经历，我们就找到了生命的真谛。"
            ];

            public static List<Article> GenerateArticles(int count = 100)
            {
                var currentId = 1;

                var faker = new Faker<Article>("zh_CN")
                    .RuleFor(a => a.Id, f => currentId++)
                    .RuleFor(c => c.LanguageCode, f => LangCode.SimplifiedChinese)
                    .RuleFor(a => a.Title, f => f.PickRandom(ChineseArticleTitles))
                    .RuleFor(a => a.ThumbnailFileId, f => f.Random.Int(1, 1000))
                    .RuleFor(a => a.Description, f => f.PickRandom(ChineseDescriptions))
                    .RuleFor(a => a.Keywords, f => f.PickRandom(ChineseKeywords))
                    .RuleFor(a => a.Views, f => f.Random.Int(50, 10000))
                    .RuleFor(a => a.Likes, f => f.Random.Int(5, 1000))
                    .RuleFor(a => a.DeliveryDate, f => f.Date.Between(DateTime.Now.AddYears(-3), DateTime.Now))
                    .RuleFor(a => a.ArticleContentCategory, f => f.PickRandom<ArticleContentCategory>())
                    .RuleFor(a => a.Status, f => f.PickRandom<PublishStatus>())
                    .RuleFor(a => a.Content, f => f.PickRandom(ChineseContentTemplates))
                    .RuleFor(a => a.Memo, f => f.Lorem.Sentence(3, 8))
                    .RuleFor(a => a.CreationTime, f => f.Date.Between(DateTime.Now.AddYears(-2), DateTime.Now))
                    .RuleFor(a => a.LastModificationTime, (f, a) => f.Date.Between(a.CreationTime, DateTime.Now));

                return faker.Generate(count);
            }
        }
    }
}