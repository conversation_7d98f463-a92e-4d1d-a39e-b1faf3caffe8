﻿using Bogus;
using HolyBless.Entities.Collections;
using HolyBless.Enums;

namespace HolyBless.FakeData.DataSeeders
{
    public static class CollectionDataHansGenerator
    {
        // Extract leaf channel IDs from mainsite.json structure
        private static readonly int[] LeafChannelIds = { 11, 21, 22, 13, 41, 42, 51, 52, 61, 62, 63, 64, 65, 66 };

        // Channel names mapping for context-appropriate collection names
        private static readonly Dictionary<int, string> ChannelNames = new()
    {
        { 11, "《梦》系列" },
        { 21, "天门开" },
        { 22, "平安赐福" },
        { 13, "爱的温暖" },
        { 41, "心灵综述" },
        { 42, "修行答疑" },
        { 51, "博客" },
        { 52, "微博" },
        { 61, "回头是岸" },
        { 62, "忆念老师" },
        { 63, "修行心得" },
        { 64, "温故知新" },
        { 65, "虔诚忏悔" },
        { 66, "神迹之光" }
    };

        public static List<Collection> GenerateCollections()
        {
            var curId = 1;
            var faker = new Faker<Collection>("zh_CN")
                .RuleFor(c => c.Id, f => curId++)
                .RuleFor(c => c.ContentCode, f => $"C{curId - 1}")
                .RuleFor(c => c.LanguageCode, f => LangCode.SimplifiedChinese)
                .RuleFor(c => c.ParentCollectionId, f => (int?)null) // Root collections
                .RuleFor(c => c.ChannelId, (f, c) => LeafChannelIds[f.IndexFaker % LeafChannelIds.Length])
                .RuleFor(c => c.ThumbnailFileId, f => f.Random.Int(1, 1000))
                .RuleFor(c => c.Name, (f, c) => GenerateCollectionName(f, c.ChannelId!.Value))
                .RuleFor(c => c.Description, (f, c) => GenerateDescription(f, c.ChannelId!.Value))
                .RuleFor(c => c.Memo, f => f.Lorem.Sentence(3, 5))
                .RuleFor(c => c.Keywords, f => string.Join(",", f.Lorem.Words(3)))
                .RuleFor(c => c.Views, f => f.Random.Int(100, 50000))
                .RuleFor(c => c.Likes, f => f.Random.Int(10, 5000))
                .RuleFor(c => c.Weight, f => f.Random.Int(0, 100))
                .RuleFor(c => c.ListStyle, f => f.PickRandom<ListStyle>())
                .RuleFor(c => c.RenderAsOneSet, f => f.Random.Bool(0.3f)) // 30% chance to render as one set
                .RuleFor(c => c.Status, f => f.PickRandom<PublishStatus>())
                .RuleFor(c => c.DefaultOrderBy, f => f.PickRandom<DefaultOrderByField>())
                .RuleFor(c => c.CreationTime, f => f.Date.Between(DateTime.Now.AddYears(-2), DateTime.Now))
                .RuleFor(c => c.LastModificationTime, (f, c) => f.Date.Between(c.CreationTime, DateTime.Now));

            return faker.Generate(LeafChannelIds.Length);
        }

        private static string GenerateCollectionName(Faker faker, int channelId)
        {
            if (!ChannelNames.TryGetValue(channelId, out _))
            {
                throw new ArgumentException($"Invalid channelId: {channelId}", nameof(channelId));
            }

            return channelId switch
            {
                11 => faker.PickRandom(
                    "梦境启示录", "梦中的智慧", "梦与现实", "梦想成真", "梦境解析",
                    "梦的力量", "梦中修行", "梦境奇遇", "梦想之路", "梦的启发"
                ),
                21 or 22 => faker.PickRandom(
                    "天门开启", "神圣之门", "天启之路", "平安祝福", "神恩浩荡",
                    "天门奇迹", "神圣护佑", "天恩赐福", "神迹显现", "天门光明"
                ),
                13 => faker.PickRandom(
                    "爱的力量", "温暖人心", "爱与慈悲", "爱的奇迹", "无私大爱",
                    "爱的温度", "爱心传递", "爱的光芒", "爱与包容", "爱的真谛"
                ),
                41 => faker.PickRandom(
                    "心灵智慧", "心灵成长", "心灵觉醒", "心灵修复", "心灵净化",
                    "心灵感悟", "心灵之旅", "心灵启迪", "心灵平静", "心灵升华"
                ),
                42 => faker.PickRandom(
                    "修行指南", "修行问答", "修行疑惑", "修行心得", "修行方法",
                    "修行体验", "修行智慧", "修行答疑", "修行困惑", "修行感悟"
                ),
                51 => faker.PickRandom(
                    "生死感悟", "人生思考", "生命真谛", "生死哲学", "人生感悟",
                    "生命意义", "生死轮回", "人生智慧", "生命觉醒", "生死超越"
                ),
                52 => faker.PickRandom(
                    "微言大义", "生活感悟", "日常思考", "微博随笔", "生活智慧",
                    "随想录", "生活点滴", "微观世界", "生活感言", "微博心语"
                ),
                61 => faker.PickRandom(
                    "回头是岸", "悔过自新", "重新开始", "迷途知返", "浪子回头",
                    "改过迁善", "重获新生", "回归正道", "忏悔重生", "重新出发"
                ),
                62 => faker.PickRandom(
                    "恩师教诲", "师恩如山", "老师的话", "师父慈悲", "恩师智慧",
                    "师恩难忘", "老师的爱", "师父教导", "恩师恩德", "师恩深重"
                ),
                63 => faker.PickRandom(
                    "修行体会", "修行感悟", "修行心得", "修行经历", "修行收获",
                    "修行感受", "修行体验", "修行感想", "修行心境", "修行领悟"
                ),
                64 => faker.PickRandom(
                    "温故知新", "回顾过往", "历史回顾", "过往回忆", "旧事重提",
                    "回首往事", "历史感悟", "过去现在", "回忆录", "历史启示"
                ),
                65 => faker.PickRandom(
                    "虔诚忏悔", "忏悔录", "悔过书", "忏悔心声", "忏悔感悟",
                    "真心忏悔", "忏悔之路", "忏悔重生", "忏悔录", "忏悔心语"
                ),
                66 => faker.PickRandom(
                    "神迹显现", "奇迹之光", "神迹见证", "神迹录", "奇迹故事",
                    "神迹体验", "奇迹见证", "神迹感悟", "奇迹人生", "神迹恩典"
                ),
                _ => string.Join(" ", faker.Lorem.Words(4))
            };
        }

        private static string GenerateDescription(Faker faker, int channelId)
        {
            string[] strings = ["记录生活的点点滴滴，分享人生的感悟与体验。"];
            var templates = channelId switch
            {
                11 =>
                [
                "记录梦境中的启示与智慧，探索梦与现实的奥秘。",
                "分享梦境中的奇遇与感悟，解析梦的深层含义。",
                "梦中的世界充满神秘，这里记录着梦境的点点滴滴。"
                ],
                21 or 22 =>
                [
                "天门开启，神恩浩荡，记录神圣的时刻与恩典。",
                "见证天门开启的奇迹，感受神圣的力量与护佑。",
                "平安祝福，神恩赐福，记录神迹显现的美好时光。"
            ],
                13 =>
                [
                "爱是世间最温暖的力量，这里记录着爱的点点滴滴。",
                "分享爱的温暖与力量，传递爱心与慈悲。",
                "爱能化解一切，这里充满着爱的温度与光芒。"
            ],
                41 =>
                [
                "心灵的成长需要智慧的指引，这里记录着心灵的感悟。",
                "探索心灵的奥秘，分享心灵成长的体验与智慧。",
                "心灵的净化与升华，记录着心灵觉醒的点点滴滴。"
            ],
                42 =>
                [
                "修行路上的疑惑与解答，为修行者指明方向。",
                "分享修行中的体验与感悟，解答修行路上的困惑。",
                "修行不易，这里记录着修行路上的问答与指导。"
            ],
                51 =>
                [
                "生死如河，人生如舟，记录着生命的感悟与思考。",
                "探索生死的奥秘，分享人生的智慧与感悟。",
                "生命的意义在于体验，这里记录着生命的点点滴滴。"
            ],
                52 =>
                [
                "微博记录生活的点滴，分享日常的感悟与思考。",
                "生活中的小感悟，微博中的大智慧。",
                "记录生活的美好时光，分享生活的智慧与感悟。"
            ],
                61 =>
                [
                "迷途知返，回头是岸，记录着重新开始的故事。",
                "分享悔过自新的体验，见证重获新生的奇迹。",
                "浪子回头金不换，这里记录着改过迁善的故事。"
            ],
                62 =>
                [
                "恩师的教诲如甘露，滋润着弟子的心田。",
                "记录着老师的慈悲与智慧，铭记师恩如山。",
                "师父的话语如明灯，照亮着前行的道路。"
            ],
                63 =>
                [
                "修行路上的体会与感悟，记录着修行的心得。",
                "分享修行中的收获与体验，记录修行的点点滴滴。",
                "修行如登山，这里记录着修行路上的心境与感受。"
            ],
                64 =>
                [
                "温故而知新，回顾过往的经历与感悟。",
                "历史的回顾与反思，从过去中汲取智慧。",
                "回首往事，感悟人生，记录着历史的启示。"
            ],
                65 =>
                [
                "虔诚忏悔，洗涤心灵，记录着忏悔的心声。",
                "真心忏悔，重获新生，分享忏悔的感悟与体验。",
                "忏悔如甘露，净化心灵，记录着忏悔的点点滴滴。"
            ],
                66 =>
                [
                "神迹显现，奇迹之光，记录着神迹的见证。",
                "见证神迹的奇妙，分享奇迹的故事与感悟。",
                "神迹如明灯，照亮前行的道路，记录着神迹的恩典。"
            ],
                _ => strings
            };

            return faker.PickRandom(templates);
        }
    }
}