﻿using Bogus;
using HolyBless.Entities.Collections;
using HolyBless.Enums;

namespace HolyBless.FakeData.DataSeeders
{
    public static class CollectionDataGeneratorHant
    {
        // Extract leaf channel IDs from mainsiteHant.json structure
        private static readonly int[] LeafChannelIds = { 1011, 1021, 1022, 1013, 1041, 1042, 1051, 1052, 1061, 1062, 1063, 1064, 1065, 1066 };

        // Channel names mapping for context-appropriate collection names
        private static readonly Dictionary<int, string> ChannelNames = new()
    {
        { 1011, "《夢》系列" },
        { 1021, "天門開" },
        { 1022, "平安賜福" },
        { 1013, "愛的溫暖" },
        { 1041, "心靈綜述" },
        { 1042, "修行答疑" },
        { 1051, "博客" },
        { 1052, "微博" },
        { 1061, "回頭是岸" },
        { 1062, "憶念老師" },
        { 1063, "修行心得" },
        { 1064, "溫故知新" },
        { 1065, "虔誠懺悔" },
        { 1066, "神蹟之光" }
    };

        public static List<Collection> GenerateCollections()
        {
            int curId = 1;
            var faker = new Faker<Collection>("zh_TW")
                .RuleFor(c => c.Id, f => 1000 + curId++)
                .RuleFor(c => c.ContentCode, f => $"C{curId - 1}")
                .RuleFor(c => c.LanguageCode, f => LangCode.TraditionalChinese)
                .RuleFor(c => c.ParentCollectionId, f => (int?)null) // Root collections
                .RuleFor(c => c.ChannelId, (f, c) => LeafChannelIds[f.IndexFaker % LeafChannelIds.Length])
                .RuleFor(c => c.ThumbnailFileId, f => (int?)null) //avoid FK error
                .RuleFor(c => c.Name, (f, c) => GenerateCollectionName(f, c.ChannelId!.Value))
                .RuleFor(c => c.Description, (f, c) => GenerateDescription(f, c.ChannelId!.Value))
                .RuleFor(c => c.Memo, f => f.Lorem.Sentence(3, 5))
                .RuleFor(c => c.Keywords, f => string.Join(",", f.Lorem.Words(3)))
                .RuleFor(c => c.Views, f => f.Random.Int(100, 50000))
                .RuleFor(c => c.Likes, f => f.Random.Int(10, 5000))
                .RuleFor(c => c.Weight, f => f.Random.Int(0, 100))
                .RuleFor(c => c.ListStyle, f => f.PickRandom<ListStyle>())
                .RuleFor(c => c.RenderAsOneSet, f => f.Random.Bool(0.3f)) // 30% chance to render as one set
                .RuleFor(c => c.Status, f => f.PickRandom<PublishStatus>())
                .RuleFor(c => c.DefaultOrderBy, f => f.PickRandom<DefaultOrderByField>())
                .RuleFor(c => c.CreationTime, f => f.Date.Between(DateTime.Now.AddYears(-2), DateTime.Now))
                .RuleFor(c => c.LastModificationTime, (f, c) => f.Date.Between(c.CreationTime, DateTime.Now));

            return faker.Generate(LeafChannelIds.Length);
        }

        private static string GenerateCollectionName(Faker faker, int channelId)
        {
            if (!ChannelNames.TryGetValue(channelId, out _))
            {
                throw new ArgumentException($"Invalid channelId: {channelId}", nameof(channelId));
            }
            return channelId switch
            {
                1011 => faker.PickRandom(
                    "夢境啟示錄", "夢中的智慧", "夢與現實", "夢想成真", "夢境解析",
                    "夢的力量", "夢中修行", "夢境奇遇", "夢想之路", "夢的啟發"
                ),
                1021 or 1022 => faker.PickRandom(
                    "天門開啟", "神聖之門", "天啟之路", "平安祝福", "神恩浩蕩",
                    "天門奇蹟", "神聖護佑", "天恩賜福", "神蹟顯現", "天門光明"
                ),
                1013 => faker.PickRandom(
                    "愛的力量", "溫暖人心", "愛與慈悲", "愛的奇蹟", "無私大愛",
                    "愛的溫度", "愛心傳遞", "愛的光芒", "愛與包容", "愛的真諦"
                ),
                1041 => faker.PickRandom(
                    "心靈智慧", "心靈成長", "心靈覺醒", "心靈修復", "心靈淨化",
                    "心靈感悟", "心靈之旅", "心靈啟迪", "心靈平靜", "心靈昇華"
                ),
                1042 => faker.PickRandom(
                    "修行指南", "修行問答", "修行疑惑", "修行心得", "修行方法",
                    "修行體驗", "修行智慧", "修行答疑", "修行困惑", "修行感悟"
                ),
                1051 => faker.PickRandom(
                    "生死感悟", "人生思考", "生命真諦", "生死哲學", "人生感悟",
                    "生命意義", "生死輪迴", "人生智慧", "生命覺醒", "生死超越"
                ),
                1052 => faker.PickRandom(
                    "微言大義", "生活感悟", "日常思考", "微博隨筆", "生活智慧",
                    "隨想錄", "生活點滴", "微觀世界", "生活感言", "微博心語"
                ),
                1061 => faker.PickRandom(
                    "回頭是岸", "悔過自新", "重新開始", "迷途知返", "浪子回頭",
                    "改過遷善", "重獲新生", "回歸正道", "懺悔重生", "重新出發"
                ),
                1062 => faker.PickRandom(
                    "恩師教誨", "師恩如山", "老師的話", "師父慈悲", "恩師智慧",
                    "師恩難忘", "老師的愛", "師父教導", "恩師恩德", "師恩深重"
                ),
                1063 => faker.PickRandom(
                    "修行體會", "修行感悟", "修行心得", "修行經歷", "修行收穫",
                    "修行感受", "修行體驗", "修行感想", "修行心境", "修行領悟"
                ),
                1064 => faker.PickRandom(
                    "溫故知新", "回顧過往", "歷史回顧", "過往回憶", "舊事重提",
                    "回首往事", "歷史感悟", "過去現在", "回憶錄", "歷史啟示"
                ),
                1065 => faker.PickRandom(
                    "虔誠懺悔", "懺悔錄", "悔過書", "懺悔心聲", "懺悔感悟",
                    "真心懺悔", "懺悔之路", "懺悔重生", "懺悔錄", "懺悔心語"
                ),
                1066 => faker.PickRandom(
                    "神蹟顯現", "奇蹟之光", "神蹟見證", "神蹟錄", "奇蹟故事",
                    "神蹟體驗", "奇蹟見證", "神蹟感悟", "奇蹟人生", "神蹟恩典"
                ),
                _ => string.Join(" ", faker.Lorem.Words(4))
            };
        }

        private static string GenerateDescription(Faker faker, int channelId)
        {
            var templates = channelId switch
            {
                1011 =>
                [
                "記錄夢境中的啟示與智慧，探索夢與現實的奧秘。",
                "分享夢境中的奇遇與感悟，解析夢的深層含義。",
                "夢中的世界充滿神秘，這裏記錄著夢境的點點滴滴。"
            ],
                1021 or 1022 =>
                [
                "天門開啟，神恩浩蕩，記錄神聖的時刻與恩典。",
                "見證天門開啟的奇蹟，感受神聖的力量與護佑。",
                "平安祝福，神恩賜福，記錄神蹟顯現的美好時光。"
            ],
                1013 =>
                [
                "愛是世間最溫暖的力量，這裏記錄著愛的點點滴滴。",
                "分享愛的溫暖與力量，傳遞愛心與慈悲。",
                "愛能化解一切，這裏充滿著愛的溫度與光芒。"
            ],
                1041 =>
                [
                "心靈的成長需要智慧的指引，這裏記錄著心靈的感悟。",
                "探索心靈的奧祕，分享心靈成長的體驗與智慧。",
                "心靈的淨化與昇華，記錄著心靈覺醒的點點滴滴。"
            ],
                1042 =>
                [
                "修行路上的疑惑與解答，為修行者指明方向。",
                "分享修行中的體驗與感悟，解答修行路上的困惑。",
                "修行不易，這裏記錄著修行路上的問答與指導。"
            ],
                1051 =>
                [
                "生死如河，人生如舟，記錄著生命的感悟與思考。",
                "探索生死的奧祕，分享人生的智慧與感悟。",
                "生命的意義在於體驗，這裏記錄著生命的點點滴滴。"
            ],
                1052 =>
                [
                "微博記錄生活的點滴，分享日常的感悟與思考。",
                "生活中的小感悟，微博中的大智慧。",
                "記錄生活的美好時光，分享生活的智慧與感悟。"
            ],
                1061 =>
                [
                "迷途知返，回頭是岸，記錄著重新開始的故事。",
                "分享悔過自新的體驗，見證重獲新生的奇蹟。",
                "浪子回頭金不換，這裏記錄著改過遷善的故事。"
            ],
                1062 =>
                [
                "恩師的教誨如甘露，滋潤著弟子的心田。",
                "記錄著老師的慈悲與智慧，銘記師恩如山。",
                "師父的話語如明燈，照亮著前行的道路。"
            ],
                1063 =>
                [
                "修行路上的體會與感悟，記錄著修行的心得。",
                "分享修行中的收穫與體驗，記錄修行的點點滴滴。",
                "修行如登山，這裏記錄著修行路上的心境與感受。"
            ],
                1064 =>
                [
                "溫故而知新，回顧過往的經歷與感悟。",
                "歷史的回顧與反思，從過去中汲取智慧。",
                "回首往事，感悟人生，記錄著歷史的啟示。"
            ],
                1065 =>
                [
                "虔誠懺悔，洗滌心靈，記錄著懺悔的心聲。",
                "真心懺悔，重獲新生，分享懺悔的感悟與體驗。",
                "懺悔如甘露，淨化心靈，記錄著懺悔的點點滴滴。"
            ],
                1066 =>
                [
                "神蹟顯現，奇蹟之光，記錄著神蹟的見證。",
                "見證神蹟的奇妙，分享奇蹟的故事與感悟。",
                "神蹟如明燈，照亮前行的道路，記錄著神蹟的恩典。"
            ],
                _ => new[] { "記錄生活的點點滴滴，分享人生的感悟與體驗。" }
            };

            return faker.PickRandom(templates);
        }
    }
}