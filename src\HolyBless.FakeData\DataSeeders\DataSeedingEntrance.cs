using HolyBless.Entities.Articles;
using HolyBless.Entities.Collections;
using HolyBless.FakeData.DataSeeders;
using HolyBless.FakeData.DataSeeders.HolyBless.FakeData.DataSeeders;

namespace HolyBless.FakeData.DataSeeders
{
    public static class DataSeedingEntrance
    {
        public static List<Collection> CollectionDataHans { get; set; } = [];
        public static List<Article> ArticleDataHans { get; set; } = [];
        public static List<CollectionToArticle> CollectionToArticleDataHans { get; set; } = [];

        public static void SeedAllData()
        {
            // 1. Generate Collections (14 collections based on leaf channels)
            CollectionDataHans = CollectionDataGenerator.GenerateCollections();

            // 2. Generate Articles (100 articles)
            ArticleDataHans = ArticleDataHansGenerator.GenerateArticles(100);

            // 3. Generate Collection-Article relationships
            CollectionToArticleDataHans = CollectionToArticleDataHansGenerator.GenerateBalancedCollectionToArticles(
                CollectionDataHans, ArticleDataHans);
        }

        /// <summary>
        /// Alternative method using specific IDs (useful when you already have existing data)
        /// </summary>
        public static void SeedWithExistingIds()
        {
            // If you already have collection IDs and article IDs from database
            var existingCollectionIds = new int[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 };
            var existingArticleIds = Enumerable.Range(1, 100).ToArray(); // 1 to 100

            CollectionToArticleDataHans = CollectionToArticleDataHansGenerator.GenerateCollectionToArticlesByIds(
                existingCollectionIds,
                existingArticleIds,
                minArticlesPerCollection: 5,
                maxArticlesPerCollection: 20);
        }
    }
}